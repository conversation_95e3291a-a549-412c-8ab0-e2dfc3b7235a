import java.util.ArrayList;
import java.util.List;

/**
 * A simple application that models the delivery and delivered lists of a shopping app.
 */
public class LazadaApp {

    // A list to hold items that are for delivery.
    private List<String> forDeliveryList;

    // A list to hold items that have been delivered.
    private List<String> deliveredList;

    /**
     * Constructs the LazadaApp and initializes the lists with some items.
     */
    public LazadaApp() {
        this.forDeliveryList = new ArrayList<>();
        this.deliveredList = new ArrayList<>();
        
        // Initialize the 'For Delivery List' with the items from the diagram.
        this.forDeliveryList.add("Acemoeds");
        this.forDeliveryList.add("Cellphone");
        this.forDeliveryList.add("Head Phone");
    }

    /**
     * Adds a new item to the for delivery list.
     * @param item The name of the item to add.
     */
    public void forDelivery(String item) {
        System.out.println("Adding '" + item + "' to the delivery list.");
        this.forDeliveryList.add(item);
    }

    /**
     * Shows all items currently in the for delivery list.
     */
    public void deliveryList() {
        System.out.println("--- For Delivery List ---");
        if (forDeliveryList.isEmpty()) {
            System.out.println("No items are currently scheduled for delivery.");
        } else {
            for (String item : forDeliveryList) {
                System.out.println("- " + item);
            }
        }
    }

    /**
     * Moves a specified item from the for delivery list to the delivered list.
     * @param item The name of the item to mark as delivered.
     */
    public void delivered(String item) {
        if (forDeliveryList.contains(item)) {
            forDeliveryList.remove(item);
            deliveredList.add(item);
            System.out.println("Successfully delivered '" + item + "'.");
        } else {
            System.out.println("Error: '" + item + "' was not found in the delivery list.");
        }
    }

    /**
     * Shows all items that have been marked as delivered.
     */
    public void deliveredList() {
        System.out.println("--- Delivered List ---");
        if (deliveredList.isEmpty()) {
            System.out.println("No items have been delivered yet.");
        } else {
            for (String item : deliveredList) {
                System.out.println("- " + item);
            }
        }
    }
}
