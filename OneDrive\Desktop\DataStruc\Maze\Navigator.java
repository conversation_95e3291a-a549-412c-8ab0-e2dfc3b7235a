package Maze;

import java.util.*;

public class Navigator {
    private Maze maze;
    private char[][] mazeGrid;
    private int rows;
    private int cols;

    // Constructor that connects to a Maze
    public Navigator(Maze maze) {
        this.maze = maze;
        this.mazeGrid = maze.getMazeGrid();
        this.rows = maze.getRows();
        this.cols = maze.getCols();
    }

    // Alternative constructor for direct grid access
    public Navigator(char[][] mazeGrid) {
        this.mazeGrid = mazeGrid;
        this.rows = mazeGrid.length;
        this.cols = mazeGrid[0].length;
    }

    // Find the shortest path from start to end using BFS
    public List<String> findPath(int startRow, int startCol, int endRow, int endCol) {
        Queue<Position> queue = new LinkedList<>();
        boolean[][] visited = new boolean[rows][cols];
        Map<Position, Position> parent = new HashMap<>();

        Position start = new Position(startRow, startCol);
        queue.offer(start);
        visited[startRow][startCol] = true;

        // Directions: up, down, left, right
        int[] dRow = {-1, 1, 0, 0};
        int[] dCol = {0, 0, -1, 1};
        String[] directions = {"UP", "DOWN", "LEFT", "RIGHT"};

        while (!queue.isEmpty()) {
            Position current = queue.poll();

            // Check if we reached the destination
            if (current.row == endRow && current.col == endCol) {
                return reconstructPath(parent, start, current, directions, dRow, dCol);
            }

            // Explore all 4 directions
            for (int i = 0; i < 4; i++) {
                int newRow = current.row + dRow[i];
                int newCol = current.col + dCol[i];

                if (isValidMove(newRow, newCol) && !visited[newRow][newCol]) {
                    visited[newRow][newCol] = true;
                    Position next = new Position(newRow, newCol);
                    parent.put(next, current);
                    queue.offer(next);
                }
            }
        }

        return new ArrayList<>(); // No path found
    }

    // Check if a move is valid (within bounds and not a wall)
    private boolean isValidMove(int row, int col) {
        return row >= 0 && row < rows && col >= 0 && col < cols &&
               mazeGrid[row][col] != '#';
    }

    // Reconstruct the path from start to end
    private List<String> reconstructPath(Map<Position, Position> parent, Position start,
                                       Position end, String[] directions, int[] dRow, int[] dCol) {
        List<String> path = new ArrayList<>();
        Position current = end;

        while (!current.equals(start)) {
            Position prev = parent.get(current);

            // Determine direction from prev to current
            for (int i = 0; i < 4; i++) {
                if (prev.row + dRow[i] == current.row && prev.col + dCol[i] == current.col) {
                    path.add(directions[i]);
                    break;
                }
            }
            current = prev;
        }

        Collections.reverse(path);
        return path;
    }

    // Find the player's current position
    public Position findPlayer() {
        for (int i = 0; i < rows; i++) {
            for (int j = 0; j < cols; j++) {
                if (mazeGrid[i][j] == 'P') {
                    return new Position(i, j);
                }
            }
        }
        return null;
    }

    // Find the exit position
    public Position findExit() {
        for (int i = 0; i < rows; i++) {
            for (int j = 0; j < cols; j++) {
                if (mazeGrid[i][j] == 'E') {
                    return new Position(i, j);
                }
            }
        }
        return null;
    }

    // Get directions from current player position to exit
    public List<String> getDirectionsToExit() {
        Position player = findPlayer();
        Position exit = findExit();

        if (player != null && exit != null) {
            return findPath(player.row, player.col, exit.row, exit.col);
        }
        return new ArrayList<>();
    }

    // Get the next best move for the player
    public String getNextMove() {
        List<String> directions = getDirectionsToExit();
        return directions.isEmpty() ? "NO_PATH" : directions.get(0);
    }

    // Display the solution path on the maze
    public void showSolution() {
        Position player = findPlayer();
        Position exit = findExit();

        if (player == null || exit == null) {
            System.out.println("Cannot find player or exit!");
            return;
        }

        List<String> path = findPath(player.row, player.col, exit.row, exit.col);

        if (path.isEmpty()) {
            System.out.println("No solution found!");
            return;
        }

        System.out.println("\n=== SOLUTION PATH ===");
        System.out.println("Steps to reach the exit: " + path.size());
        System.out.println("Directions: " + String.join(" → ", path));

        // Create a copy of the maze to show the path
        char[][] solutionMaze = new char[rows][cols];
        for (int i = 0; i < rows; i++) {
            System.arraycopy(mazeGrid[i], 0, solutionMaze[i], 0, cols);
        }

        // Mark the path
        int currentRow = player.row;
        int currentCol = player.col;

        for (String direction : path) {
            switch (direction) {
                case "UP": currentRow--; break;
                case "DOWN": currentRow++; break;
                case "LEFT": currentCol--; break;
                case "RIGHT": currentCol++; break;
            }

            if (solutionMaze[currentRow][currentCol] == ' ') {
                solutionMaze[currentRow][currentCol] = '.';
            }
        }

        // Display the solution maze
        System.out.println("\nMaze with solution path (. = path):");
        for (int i = 0; i < rows; i++) {
            for (int j = 0; j < cols; j++) {
                System.out.print(solutionMaze[i][j] + " ");
            }
            System.out.println();
        }
        System.out.println();
    }

    // Inner class to represent a position
    private static class Position {
        int row, col;

        Position(int row, int col) {
            this.row = row;
            this.col = col;
        }

        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (obj == null || getClass() != obj.getClass()) return false;
            Position position = (Position) obj;
            return row == position.row && col == position.col;
        }

        @Override
        public int hashCode() {
            return Objects.hash(row, col);
        }
    }
}
