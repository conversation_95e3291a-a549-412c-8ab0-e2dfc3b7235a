public class Main {
    public static void main(String[] args) {
        LazadaApp app = new LazadaApp();

        // 1. Show the initial state of the delivery list.
        System.out.println("Initial State:");
        app.deliveryList();
        System.out.println();

        // 2. Mark "Cellphone" as delivered, as shown in the diagram.
        System.out.println("Action: Mark 'Cellphone' as delivered.");
        app.delivered("Cellphone");
        System.out.println();

        // 3. Show the updated delivered list.
        System.out.println("Updated Lists:");
        app.deliveryList();
        app.deliveredList();
        System.out.println();
        
        // 4. Demonstrate the forDelivery method by adding a new item.
        app.forDelivery("Laptop");
        System.out.println();
        app.deliveryList();
    }
}