package Maze;
public class Maze {
    private char[][] maze;
    private int rows;
    private int cols;
    private int playerRow;
    private int playerCol;

    // Constructor to create a simple maze
    public Maze() {
        rows = 7;
        cols = 9;
        playerRow = 1;
        playerCol = 1;

        // Create a simple maze layout
        maze = new char[][] {
            {'#', '#', '#', '#', '#', '#', '#', '#', '#'},
            {'#', 'P', ' ', ' ', '#', ' ', ' ', ' ', '#'},
            {'#', '#', '#', ' ', '#', ' ', '#', '#', '#'},
            {'#', ' ', ' ', ' ', ' ', ' ', ' ', ' ', '#'},
            {'#', ' ', '#', '#', '#', '#', '#', ' ', '#'},
            {'#', ' ', ' ', ' ', ' ', ' ', ' ', 'E', '#'},
            {'#', '#', '#', '#', '#', '#', '#', '#', '#'}
        };
    }

    // Display the maze
    public void displayMaze() {
        System.out.println("\n=== SIMPLE MAZE ===");
        System.out.println("P = Player, E = Exit, # = Wall, ' ' = Path");
        System.out.println();

        for (int i = 0; i < rows; i++) {
            for (int j = 0; j < cols; j++) {
                System.out.print(maze[i][j] + " ");
            }
            System.out.println();
        }
        System.out.println();
    }

    // Move player up
    public boolean moveUp() {
        return movePlayer(playerRow - 1, playerCol);
    }

    // Move player down
    public boolean moveDown() {
        return movePlayer(playerRow + 1, playerCol);
    }

    // Move player left
    public boolean moveLeft() {
        return movePlayer(playerRow, playerCol - 1);
    }

    // Move player right
    public boolean moveRight() {
        return movePlayer(playerRow, playerCol + 1);
    }

    // Helper method to move player
    private boolean movePlayer(int newRow, int newCol) {
        // Check if the new position is valid
        if (newRow >= 0 && newRow < rows && newCol >= 0 && newCol < cols) {
            char cell = maze[newRow][newCol];

            // Check if it's not a wall
            if (cell != '#') {
                // Clear current position
                maze[playerRow][playerCol] = ' ';

                // Move to new position
                playerRow = newRow;
                playerCol = newCol;

                // Check if reached exit
                if (cell == 'E') {
                    maze[playerRow][playerCol] = 'P';
                    return true; // Won the game
                } else {
                    maze[playerRow][playerCol] = 'P';
                }
            }
        }
        return false; // Not won yet
    }

    // Check if player reached the exit
    public boolean hasWon() {
        return maze[playerRow][playerCol] == 'P' &&
               playerRow == 5 && playerCol == 7; // Exit position
    }

    // Main method to test the maze
    public static void main(String[] args) {
        Maze maze = new Maze();
        java.util.Scanner scanner = new java.util.Scanner(System.in);

        System.out.println("Welcome to the Simple Maze Game!");
        System.out.println("Use W/A/S/D to move (Up/Left/Down/Right)");
        System.out.println("Reach the 'E' to win!");

        while (true) {
            maze.displayMaze();

            if (maze.hasWon()) {
                System.out.println("🎉 Congratulations! You reached the exit!");
                break;
            }

            System.out.print("Enter move (W/A/S/D) or Q to quit: ");
            String input = scanner.nextLine().toUpperCase();

            if (input.equals("Q")) {
                System.out.println("Thanks for playing!");
                break;
            }

            switch (input) {
                case "W":
                    maze.moveUp();
                    break;
                case "S":
                    maze.moveDown();
                    break;
                case "A":
                    maze.moveLeft();
                    break;
                case "D":
                    maze.moveRight();
                    break;
                default:
                    System.out.println("Invalid input! Use W/A/S/D or Q");
            }
        }

        scanner.close();
    }
}