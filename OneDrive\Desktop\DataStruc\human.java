
String name;
    int pocketInv;
    int money;

    public Human (String n, int m){
        name = n;
        money = m;
    }

    public void scan(Apple a, <PERSON> Paul){
        int price = a.getPrice();
        if (money >= price) {
            money -= price;
            System.out.println("\n<PERSON><PERSON> brings <PERSON> the apple that costs 50 and pays " + price);
            System.out.println("<PERSON> returns <PERSON> his : " + money + " change");
        } else {
            System.out.println("Not enough money to buy the apple.");
        }
    }